{"info": {"_postman_id": "giggle-backend-api-tests", "name": "Giggle Backend API Tests", "description": "Complete API testing collection for Giggle Backend with all endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "User Management", "item": [{"name": "Register User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"profile\": {\n    \"bio\": \"Software Developer\",\n    \"location\": \"New York\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/register", "host": ["{{base_url}}"], "path": ["api", "users", "register"]}}}, {"name": "Get User Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Update User Profile", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"profile\": {\n    \"bio\": \"Senior Software Developer\",\n    \"location\": \"San Francisco\",\n    \"skills\": [\"JavaScript\", \"TypeScript\", \"Node.js\"]\n  }\n}"}, "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Delete User Profile", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/users/profile", "host": ["{{base_url}}"], "path": ["api", "users", "profile"]}}}, {"name": "Send Verification Email", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{base_url}}/api/users/send-verification-email", "host": ["{{base_url}}"], "path": ["api", "users", "send-verification-email"]}}}, {"name": "Verify User", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/users/verify-user?userId={{user_id}}&secret={{verification_secret}}", "host": ["{{base_url}}"], "path": ["api", "users", "verify-user"], "query": [{"key": "userId", "value": "{{user_id}}"}, {"key": "secret", "value": "{{verification_secret}}"}]}}}]}, {"name": "Seeker Endpoints", "item": [{"name": "Get Seeker Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/seeker/profile", "host": ["{{base_url}}"], "path": ["api", "seeker", "profile"]}}}, {"name": "Create Seeker Profile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"phoneNumber\": \"+91 9876543210\",\n  \"educationLevel\": \"bachelor\",\n  \"employmentStatus\": \"unemployed\"\n}"}, "url": {"raw": "{{base_url}}/api/seeker/profile", "host": ["{{base_url}}"], "path": ["api", "seeker", "profile"]}}}, {"name": "Update Seeker Profile", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"John <PERSON> Seeker\",\n  \"phoneNumber\": \"+91 9876543211\",\n  \"skills\": [\"JavaScript\", \"React\", \"Node.js\"]\n}"}, "url": {"raw": "{{base_url}}/api/seeker/profile", "host": ["{{base_url}}"], "path": ["api", "seeker", "profile"]}}}, {"name": "Update Basic Info (Onboarding Step 1)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"dateOfBirth\": \"1990-01-15\",\n  \"gender\": \"male\",\n  \"phoneNumber\": \"+91 9876543210\"\n}"}, "url": {"raw": "{{base_url}}/api/seeker/onboarding/basic-info", "host": ["{{base_url}}"], "path": ["api", "seeker", "onboarding", "basic-info"]}}}, {"name": "Update Location Preferences (Onboarding Step 2)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"preferences\": {\n    \"workLocation\": \"remote\",\n    \"preferredCities\": [\"Mumbai\", \"Delhi\", \"Bangalore\"],\n    \"willingToRelocate\": true\n  }\n}"}, "url": {"raw": "{{base_url}}/api/seeker/onboarding/location", "host": ["{{base_url}}"], "path": ["api", "seeker", "onboarding", "location"]}}}, {"name": "Update Education Details (Onboarding Step 3)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"education\": [\n    {\n      \"degree\": \"Bachelor of Technology\",\n      \"field\": \"Computer Science\",\n      \"institution\": \"IIT Delhi\",\n      \"startYear\": 2018,\n      \"endYear\": 2022,\n      \"grade\": \"8.5 CGPA\"\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/seeker/onboarding/education", "host": ["{{base_url}}"], "path": ["api", "seeker", "onboarding", "education"]}}}, {"name": "Update Skills (Onboarding Step 4)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"skills\": [\n    {\n      \"name\": \"JavaScript\",\n      \"level\": \"advanced\",\n      \"yearsOfExperience\": 3\n    },\n    {\n      \"name\": \"React\",\n      \"level\": \"intermediate\",\n      \"yearsOfExperience\": 2\n    }\n  ]\n}"}, "url": {"raw": "{{base_url}}/api/seeker/onboarding/skills", "host": ["{{base_url}}"], "path": ["api", "seeker", "onboarding", "skills"]}}}, {"name": "Upload Resume (Onboarding Step 5)", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"resumeLink\": \"https://example.com/resume.pdf\",\n  \"resumeFileId\": \"file_123456\"\n}"}, "url": {"raw": "{{base_url}}/api/seeker/onboarding/resume", "host": ["{{base_url}}"], "path": ["api", "seeker", "onboarding", "resume"]}}}]}, {"name": "Provider Endpoints", "item": [{"name": "Get Provider Gigs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/provider/gigs", "host": ["{{base_url}}"], "path": ["api", "provider", "gigs"]}}}, {"name": "Create Gig", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Frontend Developer\",\n  \"description\": \"Looking for an experienced React developer\",\n  \"category\": \"technology\",\n  \"subcategory\": \"web-development\",\n  \"budget\": {\n    \"min\": 50000,\n    \"max\": 80000,\n    \"currency\": \"INR\",\n    \"type\": \"fixed\"\n  },\n  \"duration\": {\n    \"value\": 3,\n    \"unit\": \"months\"\n  },\n  \"location\": {\n    \"type\": \"remote\",\n    \"city\": \"Mumbai\",\n    \"state\": \"Maharashtra\",\n    \"country\": \"India\"\n  },\n  \"requirements\": {\n    \"skills\": [\"React\", \"JavaScript\", \"CSS\"],\n    \"experience\": \"2-5 years\",\n    \"education\": \"bachelor\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/provider/gigs", "host": ["{{base_url}}"], "path": ["api", "provider", "gigs"]}}}, {"name": "Get Specific Gig", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/provider/gigs/{{gig_id}}", "host": ["{{base_url}}"], "path": ["api", "provider", "gigs", "{{gig_id}}"]}}}, {"name": "Update Gig", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Senior Frontend Developer\",\n  \"description\": \"Updated description for senior React developer position\",\n  \"budget\": {\n    \"min\": 60000,\n    \"max\": 100000,\n    \"currency\": \"INR\",\n    \"type\": \"fixed\"\n  }\n}"}, "url": {"raw": "{{base_url}}/api/provider/gigs/{{gig_id}}", "host": ["{{base_url}}"], "path": ["api", "provider", "gigs", "{{gig_id}}"]}}}, {"name": "Delete Gig", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{base_url}}/api/provider/gigs/{{gig_id}}", "host": ["{{base_url}}"], "path": ["api", "provider", "gigs", "{{gig_id}}"]}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}, {"key": "jwt_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "user_id", "value": "your_user_id_here", "type": "string"}, {"key": "verification_secret", "value": "your_verification_secret_here", "type": "string"}, {"key": "gig_id", "value": "your_gig_id_here", "type": "string"}, {"key": "seeker_token", "value": "your_seeker_jwt_token_here", "type": "string"}, {"key": "provider_token", "value": "your_provider_jwt_token_here", "type": "string"}]}