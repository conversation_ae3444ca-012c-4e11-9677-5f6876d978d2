"use strict";
/**
 * Utility functions for object manipulation
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.removeUndefinedValues = removeUndefinedValues;
exports.removeNullishValues = removeNullishValues;
exports.removeEmptyValues = removeEmptyValues;
exports.parseFacilities = parseFacilities;
/**
 * Removes undefined values from an object
 * @param obj - The object to clean
 * @returns The object with undefined values removed
 */
function removeUndefinedValues(obj) {
    const cleaned = { ...obj };
    Object.keys(cleaned).forEach(key => {
        if (cleaned[key] === undefined) {
            delete cleaned[key];
        }
    });
    return cleaned;
}
/**
 * Removes null and undefined values from an object
 * @param obj - The object to clean
 * @returns The object with null and undefined values removed
 */
function removeNullishValues(obj) {
    const cleaned = { ...obj };
    Object.keys(cleaned).forEach(key => {
        if (cleaned[key] === undefined || cleaned[key] === null) {
            delete cleaned[key];
        }
    });
    return cleaned;
}
/**
 * Removes empty values (undefined, null, empty string, empty array) from an object
 * @param obj - The object to clean
 * @returns The object with empty values removed
 */
function removeEmptyValues(obj) {
    const cleaned = { ...obj };
    Object.keys(cleaned).forEach(key => {
        const value = cleaned[key];
        if (value === undefined ||
            value === null ||
            value === "" ||
            (Array.isArray(value) && value.length === 0)) {
            delete cleaned[key];
        }
    });
    return cleaned;
}
/**
 * Parses facilities input - handles both arrays and comma-separated strings
 * @param facilities - The facilities input (array or comma-separated string)
 * @returns Array of trimmed facility strings
 */
function parseFacilities(facilities) {
    if (!facilities) {
        return [];
    }
    if (Array.isArray(facilities)) {
        return facilities.map((f) => f.trim()).filter(f => f.length > 0);
    }
    if (typeof facilities === 'string') {
        return facilities.split(',').map((f) => f.trim()).filter(f => f.length > 0);
    }
    return [];
}
